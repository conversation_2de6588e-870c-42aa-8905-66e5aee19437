import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)

// const ip = 'http://sanren.run';
// const ip = 'http://*************:8080';
// const ip = process.env["VUE_APP_BASE_URL"]
const ip = process.env["VUE_APP_BASE_URL_PICTURE"]

// 初始化 lifeData
let lifeData = uni.getStorageSync('lifeData') || {};

// 需要永久存储的状态
const saveStateKeys = [
	'vuex_account',
	'vuex_username',
	'vuex_password',
	'vuex_id',
	'vuex_ip',
	'vuex_token',
	'vuex_postIds',
	'vuex_postKeys',
	'vuex_type',
	'vuex_slider',
  'vuex_nickName',
  'vuex_phone',
  'vuex_deptName',
  'vuex_deptId'
];

// 数据持久化函数
const saveLifeData = function(key, value) {
	if(saveStateKeys.includes(key)) {
		let tmp = uni.getStorageSync('lifeData') || {};
		tmp[key] = value;
		uni.setStorageSync('lifeData', tmp);
	}
}

// 初始化 IP
if (!lifeData.vuex_ip) {
	saveLifeData('vuex_ip', ip)
}

const store = new Vuex.Store({
	state: {
		vuex_account: lifeData.vuex_account || '',
		vuex_username: lifeData.vuex_username || '',
		vuex_password: lifeData.vuex_password || '',
		vuex_id: lifeData.vuex_id || '',
		vuex_ip: lifeData.vuex_ip || ip,
		vuex_token: lifeData.vuex_token || '',
		vuex_postIds: lifeData.vuex_postIds || [],
		vuex_postKeys: lifeData.vuex_postKeys || '',
		vuex_type: lifeData.vuex_type || '',
		vuex_slider: lifeData.vuex_slider || '',
		vuex_version: '1.0.0',
		vuex_nickName: lifeData.vuex_nickName || '',
		vuex_deptId: lifeData.vuex_deptId || '',
		vuex_deptName: lifeData.vuex_deptName || '',
		vuex_appList: [],
		vuex_msg_count: {},
		vuex_phone: lifeData.vuex_phone || '',
		vuex_file_list: {}
	},
	mutations: {
		$uStore(state, payload) {
			const nameArr = payload.name.split('.');
			const saveKey = nameArr[0];

			if(nameArr.length >= 2) {
				let obj = state[saveKey];
				for(let i = 1; i < nameArr.length - 1; i++) {
					obj = obj[nameArr[i]];
				}
				obj[nameArr[nameArr.length - 1]] = payload.value;
			} else {
				state[payload.name] = payload.value;
			}

			saveLifeData(saveKey, state[saveKey]);
		}
	}
})

export default store

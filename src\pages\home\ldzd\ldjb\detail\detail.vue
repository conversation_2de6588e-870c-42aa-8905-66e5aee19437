<template>
	<view>
		<u-form :model="form" ref="uForm" class="container u-border-top" label-width="200" :label-style="labelStyle">
			<view class="p-lr-30">
				<u-form-item label="交办人:" prop="userName">
					<u-input v-model="form.userName" type="text" disabled placeholder="交办人" />
				</u-form-item>
				<u-form-item label="执行人:" prop="workUserName">
					<u-input v-model="form.workUserName" type="text" disabled placeholder="执行人" />
				</u-form-item>
				<u-form-item label="规定完成时间:" prop="limitTime">
					<u-input v-model="form.limitTime" disabled placeholder="规定完成时间"  />
				</u-form-item>
				<u-form-item label="交办内容:" prop="content" label-position="top">
					<u-input v-model="form.content" disabled type="textarea" placeholder="交办内容" />
				</u-form-item>

				<u-form-item label="备注:" :border-bottom="false" label-position="top" prop="remark">
					<u-input v-model="form.remark" type="textarea" disabled  placeholder="备注" />
				</u-form-item>
			</view>
			<!-- 间隔 -->
			<u-gap height="20" bg-color="#F5F5F5"></u-gap>
			<view class="p-lr-30">
				<u-form-item label="处理完成时间:" prop="finishTime" v-if="form.status > 0">
					<u-input v-model="form.finishTime" disabled placeholder="处理完成时间" />
				</u-form-item>
				<u-form-item label="处理结果:" prop="finishContent" label-position="top" required>
<!--					<u-input v-model="form.finishContent" type="textarea" placeholder="处理结果" :disabled="form.status != 0 && form.status != 2"/>-->
					<u-input v-model="form.finishContent" type="textarea" placeholder="处理结果"/>
				</u-form-item>
			</view>
		</u-form>
		<!-- 提交按钮 -->
		<view class="btn-box u-border-top u-flex">
			<u-button class="u-flex-1 u-m-r-20" type="primary" shape="circle" :custom-style="subStyle" v-if="form.status == 0 && form.workUserId == vuex_id" @click="handleEdit(2)">处理完成</u-button>
			<u-button class="u-flex-1" type="primary" shape="circle" :custom-style="subStyle" v-if="form.status == 2 && form.userId == vuex_id" @click="handleOver">办结</u-button>
		</view>
		<!-- 提示 -->
		<u-top-tips ref="uTips"></u-top-tips>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				params: { year: true, month: true, day: true, hour: true, minute: true, second: true },
				labelStyle: {
					color: '#808080',
					fontSize: '30rpx'
				},
				subStyle: {
					height: '86rpx',
					backgroundColor: '#327BF0'
				},
				showCarType: false,
				transportCarType: [],
				showInspectionType: false,
				inspectionTypeList: [],
				showInspectionTime: false,
				isShowFTaxiType: false,
				isShowSTaxiType: false,
				form: {},
				rules: {
					// title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
					finishContent: [{ required: true, message: '请输入处理结果', trigger: 'blur' }],
				},
				happenData: {
					tableName: 'case_transport',
					status: 1
				},
				happenFile: []
			}
		},
		computed: {
			action() {
				// #ifdef H5
				return `/prod-api/system/file/upload`
				// #endif
				// #ifndef H5
				return `${this.vuex_ip}/prod-api/system/file/upload`
				// #endif
			},
			header() {
				return {
					Authorization: this.vuex_token || this.Cookies.get('YGF-MOBILE-Token')
				}
			}
		},
		methods: {
			handleOver() {
				uni.showModal({
					title: '提示',
					content: '是否确认办结？',
					success: ({ confirm }) => {
						if (confirm) {
							this.handleEdit(9)
						}
					}
				})
			},
			validateFn(isValid) {
				// 根据传入的状态来确定是否要进行验证，用以区别暂存和办结
				return new Promise((resolve, reject) => {
					if (isValid) {
						this.$refs.uForm.validate(valid => {
							if (valid) {
								resolve()
							} else {
								reject()
							}
						})
					} else {
						resolve()
					}
				})
			},
			handleEdit(status) {
				this.validateFn(status == 2).then(() => {
					let params = { id: this.form.id, status, finishContent: this.form.finishContent }
					if (status == 2) {
						const timestamp = new Date().getTime()
						params.finishTime = this.$u.timeFormat(timestamp, 'yyyy-mm-dd hh:MM:ss')
					}
					// 开始上传
					this.$loading('数据上传中')
					this.$u.api.editLeaderTaskList(params).then(res => {
						uni.showToast({title: '操作成功'})
						uni.hideLoading()
						this.$implement()
					}).catch(() => {
						uni.hideLoading()
					})
				}).catch(() => {})
			}
		},
		onLoad(params) {
			// 获取业务数据
			if (params.id) {
				this.$loading()
				this.$u.api.getLeaderTask({}, params.id).then(res => {
					uni.hideLoading()
					this.form = { ...res.data }
				}).catch(() => {
					uni.hideLoading()
				})
			} else {
				this.form = { inspectionTime, userName: this.vuex_username, userId: this.vuex_id }
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		}
	}
</script>

<style lang="scss">
.p-lr-30 {
	padding: 0 30rpx;
	background-color: #fff;
}
.container {
	padding-bottom: 145rpx;
}
.btn-box {
	width: 100%;
	padding: 14rpx 30rpx;
	position: fixed;
	bottom: 0;
	background-color: #FFFFFF;
	z-index: 10;
}
</style>
